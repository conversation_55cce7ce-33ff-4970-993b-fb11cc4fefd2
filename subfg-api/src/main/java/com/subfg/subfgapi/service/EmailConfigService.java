package com.subfg.subfgapi.service;

import org.springframework.stereotype.Service;

import com.subfg.common.util.AesUtil;
import com.subfg.domain.entity.EmailConfig;
import com.subfg.repository.mapper.EmailConfigMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 邮箱配置管理服务
 * 提供邮箱配置的增删改查功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailConfigService {

    private final EmailConfigMapper emailConfigMapper;

    /**
     * 获取邮箱配置（解密后）
     *
     * @return 解密后的邮箱配置
     */
    public EmailConfig getEmailConfig() {
        try {
            EmailConfig config = emailConfigMapper.selectEmailConfigDecrypted();
            if (config == null) {
                log.warn("数据库中未找到邮箱配置");
                return null;
            }

            // 解密敏感信息
            decryptSensitiveFields(config);
            
            log.info("邮箱配置获取成功 - Host: {}, Port: {}", config.getHost(), config.getPort());
            return config;
            
        } catch (Exception e) {
            log.error("获取邮箱配置失败", e);
            throw new RuntimeException("获取邮箱配置失败", e);
        }
    }

    /**
     * 根据ID获取邮箱配置（解密后）
     *
     * @param id 配置ID
     * @return 解密后的邮箱配置
     */
    public EmailConfig getEmailConfigById(Integer id) {
        try {
            EmailConfig config = emailConfigMapper.selectByIdDecrypted(id);
            if (config == null) {
                log.warn("未找到ID为{}的邮箱配置", id);
                return null;
            }

            // 解密敏感信息
            decryptSensitiveFields(config);
            
            log.info("邮箱配置获取成功 - ID: {}, Host: {}", id, config.getHost());
            return config;
            
        } catch (Exception e) {
            log.error("获取邮箱配置失败 - ID: {}", id, e);
            throw new RuntimeException("获取邮箱配置失败", e);
        }
    }

    /**
     * 保存邮箱配置（加密敏感信息）
     *
     * @param emailConfig 邮箱配置
     * @return 保存结果
     */
    public boolean saveEmailConfig(EmailConfig emailConfig) {
        try {
            // 加密敏感信息
            EmailConfig encryptedConfig = encryptSensitiveFields(emailConfig);
            
            int result;
            if (encryptedConfig.getId() == null) {
                // 新增
                result = emailConfigMapper.insertEncrypted(encryptedConfig);
                log.info("新增邮箱配置成功 - Host: {}", emailConfig.getHost());
            } else {
                // 更新
                result = emailConfigMapper.updateEncrypted(encryptedConfig);
                log.info("更新邮箱配置成功 - ID: {}, Host: {}", encryptedConfig.getId(), emailConfig.getHost());
            }
            
            return result > 0;
            
        } catch (Exception e) {
            log.error("保存邮箱配置失败", e);
            throw new RuntimeException("保存邮箱配置失败", e);
        }
    }

    /**
     * 删除邮箱配置
     *
     * @param id 配置ID
     * @return 删除结果
     */
    public boolean deleteEmailConfig(Integer id) {
        try {
            int result = emailConfigMapper.deleteById(id);
            if (result > 0) {
                log.info("删除邮箱配置成功 - ID: {}", id);
                return true;
            } else {
                log.warn("删除邮箱配置失败，配置不存在 - ID: {}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("删除邮箱配置失败 - ID: {}", id, e);
            throw new RuntimeException("删除邮箱配置失败", e);
        }
    }

    /**
     * 解密敏感字段
     *
     * @param config 邮箱配置
     */
    private void decryptSensitiveFields(EmailConfig config) {
        if (config.getAccount() != null && !config.getAccount().isEmpty()) {
            String decryptedAccount = AesUtil.aesDecrypt(config.getAccount());
            config.setAccount(decryptedAccount);
        }

        if(config.getHost() != null && !config.getHost().isEmpty()){
            String decryptedHost = AesUtil.aesDecrypt(config.getHost());
            config.setHost(decryptedHost);
        }
        
        if (config.getPassword() != null && !config.getPassword().isEmpty()) {
            String decryptedPassword = AesUtil.aesDecrypt(config.getPassword());
            config.setPassword(decryptedPassword);
        }
    }

    /**
     * 加密敏感字段
     *
     * @param config 邮箱配置
     * @return 加密后的配置副本
     */
    private EmailConfig encryptSensitiveFields(EmailConfig config) {
        EmailConfig encryptedConfig = new EmailConfig();
        encryptedConfig.setId(config.getId());
        encryptedConfig.setHost(config.getHost());
        encryptedConfig.setPort(config.getPort());
        encryptedConfig.setProtocol(config.getProtocol());
        
        // 加密账号
        if (config.getAccount() != null && !config.getAccount().isEmpty()) {
            String encryptedAccount = AesUtil.aesEncrypt(config.getAccount());
            encryptedConfig.setAccount(encryptedAccount);
        }
        
        // 加密密码
        if (config.getPassword() != null && !config.getPassword().isEmpty()) {
            String encryptedPassword = AesUtil.aesEncrypt(config.getPassword());
            encryptedConfig.setPassword(encryptedPassword);
        }
        
        return encryptedConfig;
    }
}
