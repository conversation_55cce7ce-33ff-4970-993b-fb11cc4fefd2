package com.subfg.repository.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.subfg.domain.entity.User;

/**
 * 用户 Mapper 接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    User selectByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    User selectByPhone(@Param("phone") Long phone);

    /**
     * 根据微信号查询用户
     *
     * @param wechat 微信号
     * @return 用户信息
     */
    User selectByWechat(@Param("wechat") String wechat);

    /**
     * 根据用户名查询用户
     *
     * @param userName 用户名
     * @return 用户信息
     */
    User selectByUserName(@Param("userName") String userName);

    /**
     * 根据角色查询用户列表
     *
     * @param role 角色
     * @return 用户列表
     */
    List<User> selectByRole(@Param("role") String role);

    /**
     * 查询启用状态的用户列表
     *
     * @param enable 是否启用
     * @return 用户列表
     */
    List<User> selectByEnable(@Param("enable") Boolean enable);

    /**
     * 根据创建时间范围查询用户
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 用户列表
     */
    List<User> selectByCreateTimeRange(@Param("startTime") Long startTime,
                                      @Param("endTime") Long endTime);

    /**
     * 根据最后在线时间查询活跃用户
     *
     * @param lastOnlineTime 最后在线时间
     * @return 用户列表
     */
    List<User> selectActiveUsers(@Param("lastOnlineTime") Long lastOnlineTime);

    /**
     * 查询信用分数范围内的用户
     *
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * @return 用户列表
     */
    List<User> selectByCreditScoreRange(@Param("minScore") Double minScore,
                                       @Param("maxScore") Double maxScore);

    /**
     * 统计用户数量
     *
     * @param role   角色（可为空）
     * @param enable 是否启用（可为空）
     * @return 用户数量
     */
    Integer countUsers(@Param("role") String role,
                      @Param("enable") Boolean enable);

    /**
     * 更新用户最后在线时间
     *
     * @param userId         用户ID
     * @param lastOnlineTime 最后在线时间
     * @return 影响行数
     */
    int updateLastOnlineTime(@Param("userId") String userId,
                            @Param("lastOnlineTime") Long lastOnlineTime);

    /**
     * 更新用户密码
     *
     * @param userId   用户ID
     * @param password 新密码
     * @param salt     新盐值
     * @return 影响行数
     */
    int updatePassword(@Param("userId") String userId,
                      @Param("password") String password,
                      @Param("salt") String salt);

    /**
     * 更新用户状态
     *
     * @param userId 用户ID
     * @param enable 是否启用
     * @return 影响行数
     */
    int updateEnable(@Param("userId") String userId,
                    @Param("enable") Boolean enable);

    /**
     * 更新用户信用分数
     *
     * @param userId      用户ID
     * @param creditScore 信用分数
     * @return 影响行数
     */
    int updateCreditScore(@Param("userId") String userId,
                         @Param("creditScore") Double creditScore);

    /**
     * 软删除用户
     *
     * @param userId     用户ID
     * @param deleteTime 删除时间
     * @return 影响行数
     */
    int softDelete(@Param("userId") String userId,
                  @Param("deleteTime") Long deleteTime);

    /**
     * 批量更新用户状态
     *
     * @param userIds 用户ID列表
     * @param enable  是否启用
     * @return 影响行数
     */
    int batchUpdateEnable(@Param("userIds") List<String> userIds,
                         @Param("enable") Boolean enable);

    /**
     * 查询已删除的用户
     *
     * @return 已删除用户列表
     */
    List<User> selectDeletedUsers();

    /**
     * 根据IP地址查询用户
     *
     * @param ipAddress IP地址
     * @return 用户列表
     */
    List<User> selectByIpAddress(@Param("ipAddress") String ipAddress);
}
