package com.subfg.repository.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.subfg.domain.entity.FgSubstore;

/**
 * 家庭组订阅商店 Mapper 接口
 */
@Mapper
public interface FgSubstoreMapper extends BaseMapper<FgSubstore> {

    /**
     * 根据产品ID和状态查询家庭组列表
     *
     * @param productId 产品ID
     * @param status    状态（可为空）
     * @return 家庭组列表
     */
    List<FgSubstore> selectByProductIdAndStatus(@Param("productId") Integer productId,
                                                @Param("status") Integer status);

    /**
     * 根据创建用户查询家庭组列表
     *
     * @param createUser 创建用户
     * @return 家庭组列表
     */
    List<FgSubstore> selectByCreateUser(@Param("createUser") String createUser);

    /**
     * 根据预订用户查询家庭组列表
     *
     * @param bookingUserId 预订用户ID
     * @return 家庭组列表
     */
    List<FgSubstore> selectByBookingUser(@Param("bookingUserId") String bookingUserId);

    /**
     * 查询有空位的家庭组
     *
     * @param status     状态
     * @param productId  产品ID（可为空）
     * @param categoryId 分类ID（可为空）
     * @return 家庭组列表
     */
    List<FgSubstore> selectAvailableGroups(@Param("status") Integer status,
                                          @Param("productId") Integer productId,
                                          @Param("categoryId") String categoryId);

    /**
     * 根据套餐ID查询家庭组
     *
     * @param planId 套餐ID
     * @return 家庭组列表
     */
    List<FgSubstore> selectByPlanId(@Param("planId") String planId);

    /**
     * 查询即将过期的家庭组
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 家庭组列表
     */
    List<FgSubstore> selectExpiringSoon(@Param("startTime") Long startTime,
                                       @Param("endTime") Long endTime);

    /**
     * 统计家庭组数量
     *
     * @param status    状态
     * @param productId 产品ID（可为空）
     * @return 数量
     */
    Integer countByStatus(@Param("status") Integer status,
                         @Param("productId") Integer productId);

    /**
     * 更新家庭组状态
     *
     * @param familyGroupId 家庭组ID
     * @param status        状态
     * @param updateTime    更新时间
     * @return 影响行数
     */
    int updateStatus(@Param("familyGroupId") String familyGroupId,
                    @Param("status") Integer status,
                    @Param("updateTime") Long updateTime);

    /**
     * 更新空位数量
     *
     * @param familyGroupId    家庭组ID
     * @param sumVacancy       总空位数
     * @param actualJoinCount  实际加入人数
     * @param updateTime       更新时间
     * @return 影响行数
     */
    int updateVacancy(@Param("familyGroupId") String familyGroupId,
                     @Param("sumVacancy") Integer sumVacancy,
                     @Param("actualJoinCount") Integer actualJoinCount,
                     @Param("updateTime") Long updateTime);

    /**
     * 批量更新家庭组状态
     *
     * @param familyGroupIds 家庭组ID列表
     * @param status         状态
     * @param updateTime     更新时间
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("familyGroupIds") List<String> familyGroupIds,
                         @Param("status") Integer status,
                         @Param("updateTime") Long updateTime);

}
